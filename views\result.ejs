<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link rel="icon" type="image/png" href="/icon.png">
    <link rel="stylesheet" href="/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
</head>
<body>
    <!-- Floating decorative shapes -->
    <div class="floating-shape shape-1"></div>
    <div class="floating-shape shape-2"></div>
    <div class="floating-shape shape-3"></div>

    <div class="container">
        <div class="content-wrapper">
            <div class="header">
                <div class="header-icon">
                    <i class="fas fa-file-pdf" style="color: white; font-size: 1.8rem;"></i>
                </div>
                <h1><%= title %></h1>
                <p>NIM: <%= nim %> | Tahun: <%= year %> | Semester: <%= season %></p>
            </div>

        <div class="action-buttons">
            <a href="/" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Kembali
            </a>
            <% if (pdfData) { %>
                <button onclick="downloadPDF()" class="btn btn-primary">
                    <i class="fas fa-download"></i>
                    Download PDF
                </button>
            <% } %>
        </div>

        <% if (error) { %>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-triangle"></i>
                <%= error %>
            </div>
            <% if (typeof responseData !== 'undefined') { %>
                <div class="response-data">
                    <h3>Response dari server:</h3>
                    <pre><%= JSON.stringify(responseData, null, 2) %></pre>
                </div>
            <% } %>
        <% } %>

        <% if (pdfData) { %>
            <div class="pdf-container">
                <div class="pdf-controls">
                    <button onclick="prevPage()" class="btn-control" title="Halaman Sebelumnya (←)">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <span id="page-info">
                        Halaman <span id="page-num">1</span> dari <span id="page-count">-</span>
                    </span>
                    <button onclick="nextPage()" class="btn-control" title="Halaman Selanjutnya (→)">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <button onclick="zoomIn()" class="btn-control" title="Perbesar (+)">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button onclick="zoomOut()" class="btn-control" title="Perkecil (-)">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <button onclick="toggleFullHeight()" class="btn-control" id="fullheight-btn" title="Mode Full Height (F)">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </button>
                    <button onclick="fitToWidth()" class="btn-control" title="Sesuaikan Lebar (W)">
                        <i class="fas fa-arrows-alt-h"></i>
                    </button>
                </div>

                <div class="pdf-help">
                    <small>
                        <i class="fas fa-keyboard"></i>
                        Gunakan: ← → (navigasi), + - (zoom), F (full height), W (fit width)
                    </small>
                </div>
                
                <div class="pdf-viewer">
                    <canvas id="pdf-canvas" style="display: none;"></canvas>
                </div>

                <div class="loading" id="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    Memuat PDF...
                </div>
            </div>

             <center>
            <p style="font-size: 0.8rem; color: #ffffff; margin-top: 20px;">By Freack & Gum</p>
        </center>

            <script>
                // PDF data dari server
                const pdfData = '<%= pdfData %>';
                
                let pdfDoc = null;
                let pageNum = 1;
                let pageRendering = false;
                let pageNumPending = null;
                let scale = 1.5;
                let isFullHeight = false;
                const canvas = document.getElementById('pdf-canvas');
                const ctx = canvas.getContext('2d');
                const pdfViewer = document.querySelector('.pdf-viewer');

                // Load PDF
                function loadPDF() {
                    const loadingTask = pdfjsLib.getDocument({data: atob(pdfData)});
                    loadingTask.promise.then(function(pdf) {
                        pdfDoc = pdf;
                        document.getElementById('page-count').textContent = pdf.numPages;
                        document.getElementById('loading').style.display = 'none';
                        renderPage(pageNum);
                    }).catch(function(error) {
                        console.error('Error loading PDF:', error);
                        document.getElementById('loading').innerHTML = 
                            '<i class="fas fa-exclamation-triangle"></i> Error loading PDF';
                    });
                }

                // Render page
                function renderPage(num) {
                    pageRendering = true;
                    pdfDoc.getPage(num).then(function(page) {
                        const viewport = page.getViewport({scale: scale});
                        canvas.height = viewport.height;
                        canvas.width = viewport.width;

                        const renderContext = {
                            canvasContext: ctx,
                            viewport: viewport
                        };

                        const renderTask = page.render(renderContext);
                        renderTask.promise.then(function() {
                            pageRendering = false;
                            // Show canvas only after first render is complete
                            if (canvas.style.display === 'none') {
                                canvas.style.display = 'block';
                            }
                            if (pageNumPending !== null) {
                                renderPage(pageNumPending);
                                pageNumPending = null;
                            }
                        });
                    });

                    document.getElementById('page-num').textContent = num;
                }

                // Queue render page
                function queueRenderPage(num) {
                    if (pageRendering) {
                        pageNumPending = num;
                    } else {
                        renderPage(num);
                    }
                }

                // Navigation functions
                function prevPage() {
                    if (pageNum <= 1) return;
                    pageNum--;
                    queueRenderPage(pageNum);
                }

                function nextPage() {
                    if (pageNum >= pdfDoc.numPages) return;
                    pageNum++;
                    queueRenderPage(pageNum);
                }

                function zoomIn() {
                    scale += 0.25;
                    queueRenderPage(pageNum);
                }

                function zoomOut() {
                    if (scale <= 0.5) return;
                    scale -= 0.25;
                    queueRenderPage(pageNum);
                }

                function downloadPDF() {
                    const link = document.createElement('a');
                    link.href = 'data:application/pdf;base64,' + pdfData;
                    link.download = 'KHS_<%= nim %>_<%= year %>_<%= season %>.pdf';
                    link.click();
                }

                // Toggle full height mode
                function toggleFullHeight() {
                    isFullHeight = !isFullHeight;
                    const btn = document.getElementById('fullheight-btn');

                    if (isFullHeight) {
                        pdfViewer.style.maxHeight = '95vh';
                        pdfViewer.style.minHeight = '90vh';
                        btn.innerHTML = '<i class="fas fa-compress-arrows-alt"></i>';
                        btn.title = 'Mode Normal';
                    } else {
                        pdfViewer.style.maxHeight = '80vh';
                        pdfViewer.style.minHeight = '500px';
                        btn.innerHTML = '<i class="fas fa-expand-arrows-alt"></i>';
                        btn.title = 'Mode Full Height';
                    }
                }

                // Fit PDF to width
                function fitToWidth() {
                    if (pdfDoc) {
                        pdfDoc.getPage(pageNum).then(function(page) {
                            const viewport = page.getViewport({scale: 1});
                            const containerWidth = pdfViewer.clientWidth - 60; // minus padding
                            scale = containerWidth / viewport.width;
                            queueRenderPage(pageNum);
                        });
                    }
                }

                // Auto-fit on window resize
                window.addEventListener('resize', function() {
                    if (pdfDoc) {
                        setTimeout(fitToWidth, 100);
                    }
                });

                // Keyboard shortcuts
                document.addEventListener('keydown', function(e) {
                    if (pdfDoc) {
                        switch(e.key) {
                            case 'ArrowLeft':
                                e.preventDefault();
                                prevPage();
                                break;
                            case 'ArrowRight':
                                e.preventDefault();
                                nextPage();
                                break;
                            case '+':
                            case '=':
                                e.preventDefault();
                                zoomIn();
                                break;
                            case '-':
                                e.preventDefault();
                                zoomOut();
                                break;
                            case 'f':
                            case 'F':
                                e.preventDefault();
                                toggleFullHeight();
                                break;
                            case 'w':
                            case 'W':
                                e.preventDefault();
                                fitToWidth();
                                break;
                        }
                    }
                });

                // Initialize PDF when page loads
                document.addEventListener('DOMContentLoaded', loadPDF);
            </script>
        <% } %>
        </div>
    </div>
</body>
</html>
